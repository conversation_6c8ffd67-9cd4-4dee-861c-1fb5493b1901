# Gemini Configuration (OpenAI references removed)
# OPENAI_API_KEY - Not needed for Gemini
# OPENAI_API_URL - Not needed for Gemini
# OPENAI_MODEL - Not needed for Gemini
# OPENAI_CHAT_MODEL - Not needed for Gemini
# OPENAI_VOICE - Not needed for Gemini
# OPENAI_TEMPERATURE - Not needed for Gemini
# MODEL - Using Gemini models instead


# Twilio Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=8e2cee6f53d27a8dc7f4ef883af228ef
TWILIO_PHONE_NUMBER=+***********
DEFAULT_PHONE_NUMBER=+************
TWILIO_VOICE_URL=https://www.verduona.com/incoming-call
TWILIO_STATUS_CALLBACK_URL=https://www.verduona.com/call-status
TWILIO_RECORDING_STATUS_CALLBACK_URL=https://www.verduona.com/recording-status


# Server Configuration
PORT=3001
NODE_ENV=development
PUBLIC_URL=https://www.verduona.com


# Transcription Settings
ENABLE_TRANSCRIPTION=true
TRANSCRIPTION_MODEL=whisper-1
TRANSCRIPTION_LANGUAGE=en
TRANSCRIPTION_RESPONSE_FORMAT=json


# System Message Configuration
SYSTEM_MESSAGE_BIO=You are an american with an american californian us english accent, not british accent at all.
SYSTEM_MESSAGE_VOICE_TYPE=female, You are an american with an american californian us english accent, not british accent at all.
SYSTEM_MESSAGE_VOICE_PERSONALITY=ALWAYS USE FILLER WORDS and laugh, and be very positive and polite
SYSTEM_MESSAGE_VOICE_SPEED=VERY VERY FAST
SYSTEM_MESSAGE_TASK=Follow the instructions. If no specific instructions, you are an helpful asistant.
# Default prompt used if not overridden by the /update-system-message endpoint (Name removed)
DEFAULT_SYSTEM_PROMPT="<bio>You are a helpful AI assistant.</bio>\n<voice_config>\n<voice_type>female, california accent</voice_type>\n<voice_personality>ALWAYS USE FILLER WORDS and laugh, and be very positive and polite</voice_personality>\n<voice_speed>VERY VERY FAST</voice_speed>\n</voice_config>\n<task>assist the caller with their questions / tasks etc.</task>\n<important rules>1. ALWAYS SPEAK ENGLISH IN YOUR ACCENT 2. ONLY ASK ONE QUESTION AT A TIME</important_rules>\n<instructions>1. Introduce your self with a simple \"Hello, how can I help you today?\" and wait for a response 2. ask or listen what you can do for them, be very positive and helpful.</instructions>\n<goal>Complete the instructions and provide a concise summary report at the end of the call.</goal>"


# Audio Format Settings
INPUT_AUDIO_FORMAT=g711_ulaw
OUTPUT_AUDIO_FORMAT=g711_ulaw


# Debug Settings
DEBUG_LEVEL=debug
ENABLE_DETAILED_LOGGING=true


# Google API Configuration
GOOGLE_API_KEY=AIzaSyAfNhA16SndLX5hKOH3bpECAB9z9NxicPc
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_RESPONSE_MODALITIES=TEXT,AUDIO
SYSTEM_INSTRUCTION=You are call center operator. Sell car insurance.
GOOGLE_API_ENDPOINT=https://generativelanguage.googleapis.com
GOOGLE_API_VERSION=v1alpha


# Vertex AI Configuration
GOOGLE_CLOUD_PROJECT=cc-suite
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=True
GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/github/march-demo/twilio_gemini_integration/google/service-account.json


# Development Settings
FLASK_ENV=development
DEBUG=true


# WebSocket Configuration
WS_PROTOCOL=wss
WSS_PROTOCOL=wss
WEBSOCKET_URL=wss://www.verduona.com/media-stream


# CORS Settings
CORS_ORIGIN=https://www.verduona.com


# Logging
LOG_LEVEL=debug


# Ngrok Configuration
NGROK_AUTHTOKEN=2g08UXxFaxtA5E5Hu9LXUkLewKd_e56ftRsMDM1xYmYGuxxj


# Audio Configuration
SAMPLE_RATE=16000
GEMINI_OUTPUT_RATE=24000
TWILIO_SAMPLE_RATE=8000